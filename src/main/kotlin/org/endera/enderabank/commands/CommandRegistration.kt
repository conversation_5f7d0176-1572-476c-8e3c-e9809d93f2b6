package org.endera.enderabank.commands

import dev.jorel.commandapi.CommandAPICommand
import dev.jorel.commandapi.arguments.*
import dev.jorel.commandapi.executors.CommandExecutor
import dev.jorel.commandapi.executors.PlayerCommandExecutor
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import org.bukkit.Bukkit
import org.bukkit.command.CommandSender
import org.bukkit.entity.Player
import org.endera.enderabank.commands.Perms.ADMIN_PERMISSION
import org.endera.enderabank.commands.Perms.CHANGE_DESIGN_PERMISSION
import org.endera.enderabank.commands.Perms.DEPOSIT_PERMISSION
import org.endera.enderabank.commands.Perms.FBI_PERMISSION
import org.endera.enderabank.commands.Perms.NEWCARD_PERMISSION
import org.endera.enderabank.commands.Perms.PAYMENT_PERMISSION
import org.endera.enderabank.commands.Perms.RELOAD_PERMISSION
import org.endera.enderabank.commands.Perms.SUBUSER_PERMISSION
import org.endera.enderabank.commands.Perms.VIEW_TRANSACTIONS_PERMISSION
import org.endera.enderabank.commands.menus.cardsMenu
import org.endera.enderabank.commands.menus.mainMenu
import org.endera.enderabank.commands.subuser.handleSubuserCommand
import org.endera.enderabank.configutils.config
import org.endera.enderabank.database.repository.DBHolder
import org.endera.enderabank.utils.cparse
import org.endera.enderabank.warns
import org.endera.enderalib.utils.async.ioDispatcher
import org.endera.enderalib.utils.checkPermission

fun registerEbankCommands() {
    // Create all subcommands first
    val reloadCommand = CommandAPICommand("reload")
        .withPermission(RELOAD_PERMISSION)
        .executes(CommandExecutor { sender, _ ->
            reloadConfigCommand(sender)
        })

    val takeCmd = CommandAPICommand("take")
        .withPermission(ADMIN_PERMISSION)
        .withArguments(
            OfflinePlayerArgument("player"),
            IntegerArgument("amount")
        )
        .executes(CommandExecutor { sender, args ->
            takeCommand(sender, arrayOf("take", (args[0] as org.bukkit.OfflinePlayer).name ?: "", args[1].toString()))
        })

    val newcardCommand = CommandAPICommand("newcard")
        .withPermission(NEWCARD_PERMISSION)
        .withArguments(StringArgument("design").replaceSuggestions(ArgumentSuggestions.strings { _ ->
            config.designs.keys.toTypedArray()
        }))
        .executesPlayer(PlayerCommandExecutor { player, args ->
            newCardCommand(player, arrayOf("newcard", args[0] as String))
        })

    val changedesignCommand = CommandAPICommand("changedesign")
        .withPermission(CHANGE_DESIGN_PERMISSION)
        .withArguments(StringArgument("design").replaceSuggestions(ArgumentSuggestions.strings { _ ->
            config.designs.keys.toTypedArray()
        }))
        .executesPlayer(PlayerCommandExecutor { player, args ->
            changeDesignCommand(player, arrayOf("changedesign", args[0] as String))
        })

    val subuserCommand = CommandAPICommand("subuser")
        .withPermission(SUBUSER_PERMISSION)
        .withArguments(
            StringArgument("action").replaceSuggestions(ArgumentSuggestions.strings("add", "remove")),
            PlayerArgument("player")
        )
        .executesPlayer(PlayerCommandExecutor { player, args ->
            handleSubuserCommand(player, arrayOf("subuser", args[0] as String, (args[1] as Player).name))
        })

    val payCmd = CommandAPICommand("pay")
        .withPermission(PAYMENT_PERMISSION)
        .withArguments(
            PlayerArgument("player"),
            IntegerArgument("amount", 1)
        )
        .executesPlayer(PlayerCommandExecutor { player, args ->
            payCommand(player, arrayOf("pay", (args[0] as Player).name, args[1].toString()))
        })

    val transferCmd = CommandAPICommand("transfer")
        .withPermission(PAYMENT_PERMISSION)
        .withArguments(
            IntegerArgument("fromCard"),
            IntegerArgument("toCard"),
            IntegerArgument("amount", 1)
        )
        .executesPlayer(PlayerCommandExecutor { player, args ->
            transferCommand(player, arrayOf("transfer", args[0].toString(), args[1].toString(), args[2].toString()))
        })

    val dgiveCommand = CommandAPICommand("dgive")
        .withPermission("enderabank.dgive")
        .withArguments(IntegerArgument("amount"))
        .executesPlayer(PlayerCommandExecutor { player, args ->
            CoroutineScope(ioDispatcher).launch {
                val rep = DBHolder.accountsRepository
                val acc = rep.getMainAccountByName(player.name)!!
                rep.addBalance(acc.id.value, args[0] as Int)
                player.sendMessage("Success")
            }
        })

    val warnCmd = CommandAPICommand("warn")
        .withPermission(FBI_PERMISSION)
        .withArguments(
            OfflinePlayerArgument("player"),
            IntegerArgument("amount", 1),
            GreedyStringArgument("reason")
        )
        .executesPlayer(PlayerCommandExecutor { player, args ->
            warnCommand(player, arrayOf("warn", (args[0] as org.bukkit.OfflinePlayer).name ?: "", args[1].toString(), args[2] as String))
        })

    val unwarnCmd = CommandAPICommand("unwarn")
        .withPermission(FBI_PERMISSION)
        .withArguments(
            OfflinePlayerArgument("player"),
            IntegerArgument("amount", 1)
        )
        .executesPlayer(PlayerCommandExecutor { player, args ->
            unwarnCommand(player, arrayOf("unwarn", (args[0] as org.bukkit.OfflinePlayer).name ?: "", args[1].toString()))
        })

    val paywarnCmd = CommandAPICommand("paywarn")
        .withPermission(PAYMENT_PERMISSION)
        .withArguments(
            OfflinePlayerArgument("player"),
            IntegerArgument("amount", 1)
        )
        .executesPlayer(PlayerCommandExecutor { player, args ->
            paywarnCommand(player, arrayOf("paywarn", (args[0] as org.bukkit.OfflinePlayer).name ?: "", args[1].toString()))
        })

    val warnsCommand = CommandAPICommand("warns")
        .withPermission(FBI_PERMISSION)
        .withArguments(OfflinePlayerArgument("player"))
        .executesPlayer(PlayerCommandExecutor { player, args ->
            warns(player, arrayOf("warns", (args[0] as org.bukkit.OfflinePlayer).name ?: ""))
        })

    val depositCommand = CommandAPICommand("deposit")
        .withPermission(DEPOSIT_PERMISSION)
        .executesPlayer(PlayerCommandExecutor { player, _ ->
            cardsMenu(player, true)
        })

    val transactionsCommand = CommandAPICommand("transactions")
        .withPermission(VIEW_TRANSACTIONS_PERMISSION)
        .withArguments(
            LiteralArgument("view"),
            OfflinePlayerArgument("player")
        )
        .executesPlayer(PlayerCommandExecutor { player, args ->
            transactionCommands(player, arrayOf("transactions", "view", (args[1] as org.bukkit.OfflinePlayer).name ?: ""))
        })

    // Main ebank command with all subcommands
    CommandAPICommand("ebank")
        .withAliases("bank", "enderabank")
        .withPermission("enderabank.use")
        .withSubcommands(
            reloadCommand,
            takeCmd,
            newcardCommand,
            changedesignCommand,
            subuserCommand,
            payCmd,
            transferCmd,
            dgiveCommand,
            warnCmd,
            unwarnCmd,
            paywarnCmd,
            warnsCommand,
            depositCommand,
            transactionsCommand
        )
        .executesPlayer(PlayerCommandExecutor { player, _ ->
            // When no subcommand is provided, open main menu
            mainMenu(player)
        })
        .register()
}
