package org.endera.enderabank

import com.noxcrew.interfaces.InterfacesListeners
import dev.jorel.commandapi.CommandAPI
import dev.jorel.commandapi.CommandAPIBukkitConfig
import org.bukkit.Bukkit
import org.bukkit.plugin.java.JavaPlugin
import org.endera.enderabank.commands.registerEbankCommands
import org.endera.enderabank.configutils.ConfigScheme
import org.endera.enderabank.configutils.configFile
import org.endera.enderabank.configutils.defaultConfig
import org.endera.enderabank.database.initDb
import org.endera.enderabank.notifications.WarnsNotification
import org.endera.enderabank.placeholderapi.EnderaBankExpansion
import org.endera.enderalib.utils.PluginException
import org.endera.enderalib.utils.async.BukkitDispatcher
import org.endera.enderalib.utils.configuration.ConfigurationManager
import java.io.File
import java.util.logging.Logger
import kotlinx.serialization.serializer

lateinit var rlogger: Logger
lateinit var plugin: JavaPlugin
lateinit var configManager: ConfigurationManager<ConfigScheme>
lateinit var bukkitDispatcher: BukkitDispatcher

class EnderaBank : JavaPlugin() {

    override fun onLoad() {
        CommandAPI.onLoad(CommandAPIBukkitConfig(this).silentLogs(true))
        registerEbankCommands()
    }

    override fun onEnable() {
        CommandAPI.onEnable()

        plugin = this
        bukkitDispatcher = BukkitDispatcher(this)
        configFile = File("${dataFolder}/config.yml")
        rlogger = logger


        configManager = ConfigurationManager(
            configFile = configFile,
            dataFolder = dataFolder,
            defaultConfig = defaultConfig,
            logger = logger,
            serializer = ConfigScheme.serializer(),
            clazz = ConfigScheme::class
        )

        try {
            val loadedConfig = configManager.loadOrCreateConfig()
            org.endera.enderabank.configutils.config = loadedConfig
        } catch (e: PluginException) {
            logger.severe("Critical error loading configuration: ${e.message}")
            server.pluginManager.disablePlugin(this)
        }

        initDb(dataFolder)

        InterfacesListeners.install(this)

        // Register CommandAPI commands

        val pluginManager = Bukkit.getPluginManager()

        pluginManager.registerEvents(WarnsNotification(), this)


        if (Bukkit.getPluginManager().getPlugin("PlaceholderAPI") != null) {
            EnderaBankExpansion(this).register()
        } else {
            logger.warning("PlaceholderAPI not found, disabling placeholders support.")
        }

        logger.info("EnderaBank successfully loaded!")

    }
}
